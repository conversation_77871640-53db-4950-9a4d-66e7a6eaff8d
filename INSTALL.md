# دليل التثبيت - إضافة الكل في واحد

## متطلبات النظام
- Adobe Illustrator CS6 أو أحدث
- Windows 7+ أو macOS 10.9+
- 50 ميجابايت مساحة فارغة

## خطوات التثبيت

### 1. تحضير النظام

#### على Windows:
1. أغلق Adobe Illustrator تماماً
2. افتح Registry Editor (اكتب `regedit` في Start Menu)
3. انتقل إلى: `HKEY_CURRENT_USER\Software\Adobe\CSXS.9`
4. انقر بالزر الأيمن واختر `New > String Value`
5. اكتب `PlayerDebugMode` كاسم
6. انقر مرتين على المفتاح الجديد واكتب `1` كقيمة

#### على macOS:
1. أغلق Adobe Illustrator تماماً
2. افتح Terminal
3. اكتب الأمر التالي واضغط Enter:
   ```bash
   defaults write com.adobe.CSXS.9 PlayerDebugMode 1
   ```

### 2. نسخ ملفات الإضافة

#### على Windows:
انسخ مجلد الإضافة إلى أحد المواقع التالية:
```
C:\Program Files (x86)\Common Files\Adobe\CEP\extensions\
```
أو
```
C:\Users\<USER>\AppData\Roaming\Adobe\CEP\extensions\
```

#### على macOS:
انسخ مجلد الإضافة إلى أحد المواقع التالية:
```
/Library/Application Support/Adobe/CEP/extensions/
```
أو
```
~/Library/Application Support/Adobe/CEP/extensions/
```

### 3. التحقق من التثبيت
1. أعد تشغيل Adobe Illustrator
2. اذهب إلى `Window > Extensions`
3. يجب أن تجد "الكل في واحد - All in One" في القائمة
4. انقر عليها لفتح الإضافة

## استكشاف الأخطاء

### الإضافة لا تظهر في القائمة:
- تأكد من تفعيل وضع المطور (الخطوة 1)
- تأكد من نسخ الملفات في المكان الصحيح
- أعد تشغيل Illustrator
- تأكد من أن اسم المجلد هو `com.salahadoroobi.allinone`

### الإضافة تظهر لكن لا تفتح:
- تأكد من وجود جميع الملفات المطلوبة
- تحقق من أذونات الملفات
- جرب نسخ الإضافة في مجلد المستخدم بدلاً من مجلد النظام

### رسائل خطأ:
- افتح Developer Tools بالضغط على F12 داخل الإضافة
- ابحث عن رسائل الخطأ في Console
- تأكد من إصدار Illustrator المدعوم

## إلغاء التثبيت
1. أغلق Adobe Illustrator
2. احذف مجلد الإضافة من مجلد Extensions
3. أعد تشغيل Illustrator

## الدعم
للحصول على المساعدة، يرجى التواصل مع المطور أو إنشاء issue في GitHub.

---
© 2024 صلاح الدين الدروبي
