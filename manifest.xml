<?xml version="1.0" encoding="UTF-8"?>
<ExtensionManifest Version="7.0" ExtensionBundleId="com.salahadoroobi.allinone" ExtensionBundleVersion="0.0.1"
		ExtensionBundleName="الكل في واحد" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<ExtensionList>
		<Extension Id="com.salahadoroobi.allinone.panel" Version="0.0.1" />
	</ExtensionList>
	<ExecutionEnvironment>
		<HostList>
			<Host Name="ILST" Version="[17.0,99.9]" />
		</HostList>
		<LocaleList>
			<Locale Code="All" />
		</LocaleList>
		<RequiredRuntimeList>
			<RequiredRuntime Name="CSXS" Version="7.0" />
		</RequiredRuntimeList>
	</ExecutionEnvironment>
	<DispatchInfoList>
		<Extension Id="com.salahadoroobi.allinone.panel">
			<DispatchInfo>
				<Resources>
					<MainPath>./index.html</MainPath>
					<ScriptPath>./jsx/hostscript.jsx</ScriptPath>
				</Resources>
				<Lifecycle>
					<AutoVisible>true</AutoVisible>
				</Lifecycle>
				<UI>
					<Type>Panel</Type>
					<Menu>الكل في واحد - All in One</Menu>
					<Geometry>
						<Size>
							<Height>400</Height>
							<Width>300</Width>
						</Size>
						<MinSize>
							<Height>200</Height>
							<Width>250</Width>
						</MinSize>
						<MaxSize>
							<Height>800</Height>
							<Width>600</Width>
						</MaxSize>
					</Geometry>
					<Icons>
						<Icon Type="Normal">./icons/icon-normal.png</Icon>
						<Icon Type="RollOver">./icons/icon-rollover.png</Icon>
						<Icon Type="DarkNormal">./icons/icon-dark-normal.png</Icon>
						<Icon Type="DarkRollOver">./icons/icon-dark-rollover.png</Icon>
					</Icons>
				</UI>
			</DispatchInfo>
		</Extension>
	</DispatchInfoList>
</ExtensionManifest>
